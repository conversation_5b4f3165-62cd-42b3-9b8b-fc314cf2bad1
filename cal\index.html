<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Calculator with Horizontal Parameter Comparison</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
        input { width: 80px; padding: 4px; }
        button { margin: 10px 5px; padding: 8px 12px; }
        .config-name { font-weight: bold; }
    </style>
</head>
<body>
    <h1>Price Calculator - Horizontal Parameter Comparison</h1>
    <button id="addConfig">Add New Configuration</button>
    <button id="removeConfig">Remove Last Configuration</button>

    <table id="comparisonTable">
        <thead>
            <tr id="headerRow">
                <th>Parameter</th>
                <th>Config 1</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="config-name">Base Price (RM)</td>
                <td><input type="number" class="basePrice" value="25.00" step="0.01"></td>
            </tr>
            <tr>
                <td class="config-name">Total Trip Distance (km)<br><small>10km内不加价，10-20km 1.2%/km，之后每10km per km增加1%然后1.2%</small></td>
                <td><input type="number" class="distance" value="67.85" step="0.01"></td>
            </tr>
            <tr>
                <td class="config-name">Distance Increase (RM)<br><small>距离加价 = 基价 * 百分比</small></td>
                <td id="distInc1">54.81</td>
            </tr>
            <tr>
                <td class="config-name">Percentage Increase<br><small>加价百分比 = per km百分比 * (距离-10)，per km百分比根据区间调整</small></td>
                <td id="blocks1">219.25%</td>
            </tr>
            <tr>
                <td class="config-name">Multiplier<br><small>倍数 = 1 + 加价百分比</small></td>
                <td id="mult1">3.1925</td>
            </tr>
            <tr>
                <td class="config-name">Adjusted Base (RM)<br><small>调整后基价 = 原始基价 * 倍数</small></td>
                <td id="adjBase1">79.81</td>
            </tr>
            <tr>
                <td class="config-name">Regional Increase (RM)<br><small>区域加价 = 55 (如果距离>10km)</small></td>
                <td id="regional1">55.00</td>
            </tr>
            <tr>
                <td class="config-name">Final Price (RM)</td>
                <td id="final1">134.81</td>
            </tr>
            <tr>
                <td class="config-name">Calculation<br><small>详细计算过程</small></td>
                <td id="calc1">25.00 * 219.25% = 54.81<br>25.00 + 54.81 + 55.00 + 0.00 = 134.81</td>
            </tr>
        </tbody>
    </table>

    <script>
        let configCount = 1;

        function calculateAll() {
            const rows = document.querySelectorAll('#comparisonTable tbody tr');
            for (let col = 1; col <= configCount; col++) {
                const basePrice = parseFloat(rows[0].querySelector(`td:nth-child(${col + 1}) input`).value) || 0;
                const distance = parseFloat(rows[1].querySelector(`td:nth-child(${col + 1}) input`).value) || 0;

                // Fixed values
                const regionalIncrease = 55;
                const specialIncrease = 0;
                const inTawauSemporna = 0; // Assume not in Tawau/Semporna

                // Dynamic distance increase logic based on analyzed pattern
                let extraKm = Math.max(0, distance - 10);
                let blocks = Math.floor(extraKm / 10);
                let perKmPercent;
                if (blocks === 0) {
                    perKmPercent = 0.012; // 10-20km: 1.2%
                } else {
                    perKmPercent = 0.012 + 0.01 + 0.012 * (blocks - 1); // First +1%, then +1.2% per block
                }
                let percent = perKmPercent * extraKm;
                const distanceIncrease = basePrice * percent;
                const adjustedBase = basePrice + distanceIncrease;
                const factor = 1 + percent;

                // Regional increase only if distance > 10km
                const regional = (distance > 10 && inTawauSemporna === 0) ? regionalIncrease : 0;

                const finalPrice = adjustedBase + regional + specialIncrease;

                // write distance increase, percent, multiplier, adjusted base, regional, final, calc
                rows[2].querySelector(`td:nth-child(${col + 1})`).textContent = distanceIncrease.toFixed(2);
                rows[3].querySelector(`td:nth-child(${col + 1})`).textContent = (percent * 100).toFixed(2) + '%';
                rows[4].querySelector(`td:nth-child(${col + 1})`).textContent = factor.toFixed(4);
                rows[5].querySelector(`td:nth-child(${col + 1})`).textContent = adjustedBase.toFixed(2);
                rows[6].querySelector(`td:nth-child(${col + 1})`).textContent = regional.toFixed(2);
                rows[7].querySelector(`td:nth-child(${col + 1})`).textContent = finalPrice.toFixed(2);

                // detailed calculation
                const calcText = `${basePrice.toFixed(2)} * ${(percent * 100).toFixed(2)}% = ${distanceIncrease.toFixed(2)}<br>${basePrice.toFixed(2)} + ${distanceIncrease.toFixed(2)} + ${regional.toFixed(2)} + ${specialIncrease.toFixed(2)} = ${finalPrice.toFixed(2)}`;
                rows[8].querySelector(`td:nth-child(${col + 1})`).innerHTML = calcText;
            }
        }

        function addConfig() {
            configCount++;
            const headerRow = document.getElementById('headerRow');
            const newTh = document.createElement('th');
            newTh.textContent = `Config ${configCount}`;
            headerRow.appendChild(newTh);

            const rows = document.querySelectorAll('#comparisonTable tbody tr');
            rows.forEach((row, index) => {
                const newTd = document.createElement('td');
                if (index < 2) {
                    const input = document.createElement('input');
                    input.type = 'number';
                    input.step = '0.01';
                    input.className = row.querySelector('input').className;
                    input.value = row.querySelector('input').value;
                    input.addEventListener('input', calculateAll);
                    newTd.appendChild(input);
                } else if (index === 6) {
                    newTd.textContent = '55.00';
                } else if (index === 8) {
                    newTd.innerHTML = '0.00 * 0.00% = 0.00<br>0.00 + 0.00 + 0.00 + 0.00 = 0.00';
                } else {
                    newTd.textContent = '0.00';
                }
                row.appendChild(newTd);
            });
            calculateAll();
        }

        function removeConfig() {
            if (configCount > 1) {
                configCount--;
                const headerRow = document.getElementById('headerRow');
                headerRow.removeChild(headerRow.lastChild);

                const rows = document.querySelectorAll('#comparisonTable tbody tr');
                rows.forEach(row => {
                    row.removeChild(row.lastChild);
                });
            }
        }

        document.getElementById('addConfig').addEventListener('click', addConfig);
        document.getElementById('removeConfig').addEventListener('click', removeConfig);

        // Add event listeners to existing inputs
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', calculateAll);
        });

        // Initial calculation
        calculateAll();
    </script>
</body>
</html>