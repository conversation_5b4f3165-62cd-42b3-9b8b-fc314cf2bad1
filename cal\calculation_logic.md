# Price Calculator Calculation Logic

## Overview
This document describes the calculation logic for the price calculator implemented in the HTML page. The calculator determines the final price based on base price, trip distance, and regional factors.

## Key Parameters
- **Base Price**: The starting price in RM
- **Total Trip Distance**: Distance in km
- **Regional Increase**: Fixed at 55 RM if distance > 10km
- **Special Increase**: Fixed at 0 RM

## Distance Increase Logic

### Basic Rules
- No increase for distances ≤ 10km
- For distances > 10km, calculate percentage increase based on distance tiers

### Percentage Calculation
```javascript
let extraKm = Math.max(0, distance - 10);
let blocks = Math.floor(extraKm / 10);
let perKmPercent;

if (blocks === 0) {
    perKmPercent = 0.012; // 10-20km: 1.2% per km
} else {
    perKmPercent = 0.012 + 0.01 + 0.012 * (blocks - 1); // First +1%, then +1.2% per block
}

let percent = perKmPercent * extraKm;
```

### Tier Examples
- **10-20km**: 1.2% per km
- **20-30km**: 2.2% per km (1.2% + 1%)
- **30-40km**: 3.4% per km (2.2% + 1.2%)
- **40-50km**: 4.6% per km (3.4% + 1.2%)
- **50-60km**: 5.8% per km (4.6% + 1.2%)
- And so on, increasing by 1.2% per 10km block

## Final Price Calculation
```javascript
let distanceIncrease = basePrice * percent;
let adjustedBase = basePrice + distanceIncrease;
let regional = (distance > 10) ? 55 : 0;
let finalPrice = adjustedBase + regional + 0; // specialIncrease = 0
```

## Example Calculation
For Base Price = 25 RM, Distance = 67.85 km:

1. Extra km = 67.85 - 10 = 57.85
2. Blocks = floor(57.85 / 10) = 5
3. Per km percent = 0.012 + 0.01 + 0.012 * 4 = 0.012 + 0.01 + 0.048 = 0.07 (7%)
4. Total percent = 0.07 * 57.85 ≈ 4.05 (405%)
5. Distance increase = 25 * 4.05 ≈ 101.25
6. Adjusted base = 25 + 101.25 = 126.25
7. Regional = 55 (since distance > 10)
8. Final price = 126.25 + 55 = 181.25 RM

## Implementation Notes
- All calculations are performed in real-time as inputs change
- The HTML page includes a table for horizontal comparison of multiple configurations
- Regional increase is only applied for distances exceeding 10km
- The percentage increases dynamically based on distance blocks
