<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>KLIA 1 Van Pick-Up Guide — GMH</title>
    <style>
      :root {
        /* Theme derived from Letterhead.png (GoMyHire purple family) */
        --brand: #8e2a8d;       /* primary */
        --brand-600: #741d73;   /* darker for text/icons */
        --brand-100: #f6e9f8;   /* light tint for backgrounds */
        --accent: #b64fc1;      /* gradient partner */
        --ink: #1f2230;
        --muted: #666b7a;
        --card: #ffffff;
        --bg: #f7f7fb;
        --border: #e1e3ef;
      }

      html, body {
        margin: 0; padding: 0; color: var(--ink);
        font-family: system-ui, -apple-system, Segoe UI, Roboto, Aria<PERSON>, "Noto Sans", sans-serif;
        line-height: 1.45;
      }
      /* Subtle gradient background to enhance glass blur */
      body {
        background: radial-gradient(800px 800px at 10% -10%, rgba(182, 79, 193, 0.18), transparent 60%),
                    radial-gradient(900px 900px at 90% 110%, rgba(142, 42, 141, 0.14), transparent 60%),
                    linear-gradient(180deg, #efeaf7, #f7f7fb);
      }
      .container { max-width: 1200px; margin: 24px auto 64px; padding: 0 16px; }

      /* Language toggle visibility rules */
      body[data-lang="en"] .cn { display: none !important; }
      body[data-lang="zh"] .en { display: none !important; }

      /* Language toggle button */
      .lang-toggle {
        position: fixed; top: 0; right: 0; z-index: 1000;
        background: var(--brand); color: #fff; border: none;
        border-radius: 999px; padding: 3px 6px; font-weight: 700;
        font-size: 0.75rem; /* ~50% smaller */
        cursor: pointer; box-shadow: 0 1px 2px rgba(0,0,0,.08);
        opacity: 0.4; /* 40% opacity as requested */
      }
      .lang-toggle:focus { outline: 2px solid #d9b6e0; outline-offset: 2px; }

  header {
        border-radius: 14px;
        padding: 20px 24px;
        display: grid; gap: 8px;
    position: relative;
      }
  header::after { content: ""; height: 6px; border-radius: 0 0 12px 12px; display: block; background: linear-gradient(90deg, var(--brand) 0%, var(--accent) 70%); margin: 8px -24px -20px; }
  .letterhead { margin-bottom: 8px; }
  .letterhead-embed { width: 100%; height: 90px; border: none; display: block; }
  .letterhead-fallback { width: 100%; height: auto; display: block; }
  .header-img { width: 100%; height: auto; display: block; }
  .footer-banner { margin-top: 16px; }
  .footer-img { width: 100%; height: auto; display: block; }
      .brand {
        display: flex; align-items: center; gap: 12px;
      }
      .brand-badge {
        width: 42px; height: 42px; border-radius: 50%;
        display: grid; place-items: center;
        background: var(--brand); color: #fff;
        font-weight: 700;
      }
      h1 { font-size: 1.5rem; margin: 0; }
      .subtitle { color: var(--muted); font-size: .95rem; }

      .meta {
        display: flex; gap: 16px; flex-wrap: wrap; color: var(--muted);
        font-size: .9rem;
      }

  .steps { margin-top: 20px; display: grid; gap: 28px; counter-reset: step; grid-template-columns: 1fr; }
  @media (min-width: 980px) {
    .steps { grid-template-columns: 1fr 1fr; }
  }
      .step {
        border-radius: 12px;
    padding: 14px 14px 16px;
        display: grid; grid-template-columns: 56px 1fr; gap: 14px;
        align-items: start;
    position: relative;
      }
  /* Connector arrows between steps */
  .steps .step::after {
    content: ""; position: absolute; bottom: -18px; left: 50%; transform: translateX(-50%);
    width: 24px; height: 24px; border-radius: 50%;
    display: grid; place-items: center;
    color: var(--brand-600); font-weight: 800; font-size: 14px;
    background: rgba(255,255,255,0.6);
    border: 1px solid rgba(255,255,255,0.45);
    -webkit-backdrop-filter: blur(8px) saturate(120%);
    backdrop-filter: blur(8px) saturate(120%);
    box-shadow: 0 6px 16px rgba(17,12,46,0.08), 0 2px 6px rgba(17,12,46,0.06);
  }
  .steps .step:last-child::after { display: none; }
  /* Two-column: odd -> right arrow, even -> down arrow */
  @media (min-width: 980px) {
    .steps .step:nth-child(odd)::after {
      content: "→";
      top: 50%; bottom: auto; left: auto; right: -18px; transform: translateY(-50%);
    }
    .steps .step:nth-child(even)::after {
      content: "↓";
      bottom: -18px; left: 50%; right: auto; top: auto; transform: translateX(-50%);
    }
  }
  /* One-column: all down arrows */
  @media (max-width: 979px) {
    .steps .step::after { content: "↓"; }
  }
  .step::before { content: ""; position: absolute; left: 0; top: 0; bottom: 0; width: 4px; border-radius: 12px 0 0 12px; background: linear-gradient(180deg, var(--brand), var(--accent)); }
      .icon {
        width: 56px; height: 56px; border-radius: 10px; background: var(--brand-100);
        display: grid; place-items: center; border: 1px solid var(--border);
      }
      .step h3 { margin: 2px 0 6px; font-size: 1.08rem; color: var(--brand-600); }
      .steps .step h3::before { counter-increment: step; content: counter(step) ". "; color: var(--brand); font-weight: 800; }
      .step p { margin: 0; }
      .caption { color: var(--muted); font-size: .92rem; margin-top: 6px; }

  .photo { margin-top: 10px; border: 1px solid rgba(255,255,255,0.45); border-radius: 12px; background: #fff; overflow: hidden; box-shadow: 0 2px 8px rgba(17,12,46,0.08); }
  .photo-img { width: 100%; height: auto; display: block; }

  .note { margin-top: 18px; color: var(--muted); font-size: .92rem; padding: 10px 12px; border-radius: 12px; border: 1px solid rgba(255,255,255,0.4); background: rgba(255,255,255,0.5); -webkit-backdrop-filter: blur(10px) saturate(120%); backdrop-filter: blur(10px) saturate(120%); box-shadow: 0 6px 16px rgba(17,12,46,0.06), 0 1px 4px rgba(17,12,46,0.05); }

      footer { margin-top: 26px; color: var(--muted); font-size: .84rem; text-align: center; border-radius: 12px; padding: 12px; }

  /* Gallery */
  .gallery { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 12px; margin-top: 10px; }
  .gallery figure { margin: 0; border: 1px solid var(--border); border-radius: 12px; overflow: hidden; background: #fff; box-shadow: 0 1px 2px rgba(0,0,0,.04); }
  .gallery img { width: 100%; height: auto; display: block; }
  .gallery figcaption { font-size: .83rem; color: var(--muted); padding: 6px 8px; }
      /* Print */
      @media print {
        body { background: #fff; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
        .container { max-width: none; margin: 0; padding: 0; }
        header, .step, .gallery figure { box-shadow: none; }
        .photo { height: 140px; }
        @page { size: A4; margin: 16mm; }
      }

      /* Glassmorphism: apply to header, step cards, gallery items, and footer */
      header, .step, .gallery figure, footer {
        background: rgba(255,255,255,0.55);
        border: 1px solid rgba(255,255,255,0.4);
        box-shadow: 0 8px 24px rgba(17,12,46,0.08), 0 2px 6px rgba(17,12,46,0.06);
        -webkit-backdrop-filter: blur(12px) saturate(120%);
        backdrop-filter: blur(12px) saturate(120%);
      }
    </style>
  </head>
  <body data-lang="en">
    <div class="container">
      <header>
        <button id="langToggle" class="lang-toggle" aria-label="Toggle language">EN / 中文</button>
        <div class="letterhead" aria-hidden="true">
          <img class="header-img" src="../Letterhead.png" alt="Letterhead" />
        </div>
        <div class="brand">
          <div>
            <h1>
              <span class="en">KLIA 1 Van Pick-Up Guide</span>
              <span class="cn">KLIA 1 机场商旅车接载指引</span>
            </h1>
            <div class="subtitle">
              <span class="en">Step-by-step instructions to reach the pick-up point</span>
              <span class="cn">逐步引导至接载点</span>
            </div>
          </div>
        </div>
        <div class="meta">
          <div class="en"><strong>Terminal:</strong> KLIA 1 (International Arrivals)</div>
          <div class="cn"><strong>航站楼：</strong> KLIA 1（国际到达）</div>
        </div>
      </header>
        <div class="note en">Tip: If unsure, ask airport staff for “Door 4 pick-up at Level 1, KLIA 1”.</div>
        <div class="note cn">小贴士：如不确定，可向机场工作人员询问“KLIA 1 一层 4 号门接客点”。</div>

      <main class="steps" aria-label="Guide steps">
        <!-- Step 1 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Gate icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" role="img">
              <rect x="3" y="3" width="18" height="14" rx="2"/>
              <path d="M7 17v4M17 17v4M3 13h18"/>
            </svg>
          </div>
          <div>
            <h3 class="en">1. Exit the International Arrival Gate and walk towards Door 5</h3>
            <p class="cn">1. 走出国际到达出口，前往 5 号门</p>
            <p class="caption en">Follow terminal signage for Doors 4–6; keep right-hand side vantage from the arrivals hall.</p>
            <p class="caption cn">请沿指示牌前往 4–6 号门；从到达大厅的右侧行进。</p>
            <div class="photo"><img class="photo-img" src="./klia-01.jpg" alt="Arrival Gate towards Door 5" /></div>
          </div>
        </section>

        <!-- Step 2 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Arrow turn icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 4l5 5-5 5"/>
              <path d="M20 9H9a5 5 0 0 0-5 5v6"/>
            </svg>
          </div>
          <div>
            <h3 class="en">2. Take a left in front of Door 5 and walk towards Coffee Bean</h3>
            <p class="cn">2. 在 5 号门前左转，步行至 Coffee Bean 咖啡店</p>
            <p class="caption en">You should see Coffee Bean on your path after turning left.</p>
            <p class="caption cn">左转后不久即可看到 Coffee Bean。</p>
            <div class="photo"><img class="photo-img" src="./klia-02.jpg" alt="Coffee Bean storefront near Door 5" /></div>
          </div>
        </section>

        <!-- Step 3 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Coffee icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 10h13a4 4 0 0 1 0 8H8a5 5 0 0 1-5-5v-3z"/>
              <path d="M16 10V7"/>
              <path d="M12 10V6"/>
              <path d="M8 10V8"/>
            </svg>
          </div>
          <div>
            <h3 class="en">3. From Coffee Bean, walk towards the lifts</h3>
            <p class="cn">3. 从 Coffee Bean 出发，朝电梯方向前行</p>
            <p class="caption en">Continue straight; lift lobbies are signposted as Lift C and Lift D.</p>
            <p class="caption cn">继续直行，电梯大厅标注为 C 与 D。</p>
            <div class="photo"><img class="photo-img" src="./klia-03.jpg" alt="View from Coffee Bean towards Lift lobby" /></div>
          </div>
        </section>

        <!-- Step 4 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Elevator icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round">
              <rect x="4" y="3" width="16" height="18" rx="2"/>
              <rect x="8" y="7" width="8" height="9" rx="1"/>
              <path d="M12 4v2M9 4v2M15 4v2"/>
            </svg>
          </div>
          <div>
            <h3 class="en">4. Take Lift C or D and proceed to Level 1</h3>
            <p class="cn">4. 乘坐 C 或 D 电梯，前往 1 层</p>
            <p class="caption en">Level 1 is the pick-up level.</p>
            <p class="caption cn">1 层为接客层。</p>
            <div class="photo"><img class="photo-img" src="./klia-05.jpg" alt="Step 4 photo 1" /></div>
            <div class="photo"><img class="photo-img" src="./klia-06.jpg" alt="Step 4 photo 2" /></div>
          </div>
        </section>

        <!-- Step 5 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Walk/route icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 20c4-6 8-6 12-12"/>
              <circle cx="16.5" cy="6.5" r="2"/>
            </svg>
          </div>
          <div>
            <h3 class="en">5. Exit the lift and continue straight towards Door 4</h3>
            <p class="cn">5. 出电梯后直行，前往 4 号门</p>
            <p class="caption en">Follow the signs to Door 4; stay on the main walkway.</p>
            <p class="caption cn">按照指示牌前往 4 号门，沿主通道直行。</p>
            <div class="photo"><img class="photo-img" src="./klia-07.jpg" alt="Step 5 photo" /></div>
          </div>
        </section>

        <!-- Step 6 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Door icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="3" width="12" height="18" rx="1"/>
              <circle cx="14" cy="12" r=".8"/>
            </svg>
          </div>
          <div>
            <h3 class="en">6. Exit through Door 4 to the pick-up location</h3>
            <p class="cn">6. 通过 4 号门出站，抵达接客区</p>
            <p class="caption en">You are now at the designated pick-up area for vans.</p>
            <p class="caption cn">这里就是面包车的指定接载点。</p>
            <div class="photo"><img class="photo-img" src="./klia-08.jpg" alt="Step 6 photo 1" /></div>
            <div class="photo"><img class="photo-img" src="./klia-09.jpg" alt="Step 6 photo 3" /></div>
            <div class="photo"><img class="photo-img" src="./klia-10.jpg" alt="Step 6 photo 4" /></div>
          </div>
        </section>

        <!-- Step 7 -->
  <section class="step">
          <div class="icon" aria-hidden="true">
            <!-- Van icon -->
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="#0a6cf1" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="8" width="13" height="8" rx="2"/>
              <path d="M16 12h3l2 2v2h-5z"/>
              <circle cx="7.5" cy="17" r="1.6"/>
              <circle cx="17.5" cy="17" r="1.6"/>
            </svg>
          </div>
          <div>
            <h3 class="en">7. Wait for your van at the pick-up location</h3>
            <p class="cn">7. 在接客区等候您的面包车</p>
            <p class="caption en">Stand by the curb; ensure your phone is reachable for driver coordination.</p>
            <p class="caption cn">请在路沿边等候，并保持电话畅通以便与司机联络。</p>
            <div class="photo"><img class="photo-img" src="./klia-11.jpg" alt="Step 7 photo" /></div>
          </div>
        </section>

        

        
      </main>

      <footer>
        <div class="footer-banner" aria-hidden="true">
          <img class="footer-img" src="../footer.png" alt="GoMyHire Offices and Contacts" />
        </div>
      </footer>
    </div>
    <script>
      (function() {
        var STORAGE_KEY = 'guide_lang';
        var body = document.body;
        var btn = document.getElementById('langToggle');
        function setBtnLabel(lang) {
          // Keep a neutral label; could localize if desired
          btn.setAttribute('aria-label', lang === 'zh' ? '切换至英文' : 'Switch to Chinese');
        }
        try {
          var saved = localStorage.getItem(STORAGE_KEY);
          if (saved === 'en' || saved === 'zh') {
            body.setAttribute('data-lang', saved);
            setBtnLabel(saved);
          }
        } catch (e) { /* ignore storage errors */ }

        btn.addEventListener('click', function() {
          var current = body.getAttribute('data-lang') === 'zh' ? 'en' : 'zh';
          body.setAttribute('data-lang', current);
          try { localStorage.setItem(STORAGE_KEY, current); } catch (e) {}
          setBtnLabel(current);
        });
      })();
    </script>
  </body>
</html>
