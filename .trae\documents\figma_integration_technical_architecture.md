# Figma 集成工具技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[React 前端应用]
    B --> C[Express.js 后端服务]
    C --> D[Supabase 数据库]
    C --> E[Figma REST API]
    C --> F[Redis 缓存]
    C --> G[文件存储系统]

    subgraph "前端层"
        B
    end

    subgraph "后端层"
        C
        F
    end

    subgraph "数据层"
        D
        G
    end

    subgraph "外部服务"
        E
    end
```

## 2. 技术描述

- **前端**: React@18 + TypeScript + Tailwind CSS + Vite + React Router + Zustand
- **后端**: Express.js@4 + TypeScript + Node.js@18
- **数据库**: Supabase (PostgreSQL)
- **缓存**: Redis@7
- **外部 API**: Figma REST API
- **文件存储**: 本地文件系统 + Supabase Storage
- **构建工具**: Vite + ESBuild
- **代码质量**: ESLint + Prettier + <PERSON><PERSON>

## 3. 路由定义

| 路由 | 用途 |
|------|------|
| / | 首页重定向到仪表板 |
| /dashboard | 仪表板页面，显示项目概览和快速操作 |
| /tokens | 设计令牌管理页面，管理和编辑设计令牌 |
| /tokens/:id | 单个令牌详情和编辑页面 |
| /components | 组件库页面，展示所有设计组件 |
| /components/:id | 单个组件详情和代码生成页面 |
| /sync | 同步配置页面，管理 Figma 连接和同步设置 |
| /settings | 项目设置页面，用户管理和系统配置 |
| /settings/users | 用户管理子页面 |
| /settings/integrations | 集成配置子页面 |
| /login | 用户登录页面 |
| /register | 用户注册页面 |
| /profile | 用户个人资料页面 |

## 4. API 定义

### 4.1 核心 API

#### 用户认证相关

```
POST /api/auth/login
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| email | string | true | 用户邮箱 |
| password | string | true | 用户密码 |

响应参数:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| success | boolean | 登录是否成功 |
| token | string | JWT 访问令牌 |
| user | object | 用户信息对象 |

示例:
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

```
POST /api/auth/register
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| email | string | true | 用户邮箱 |
| password | string | true | 用户密码 |
| name | string | true | 用户姓名 |
| role | string | true | 用户角色 (designer/developer/admin) |

#### Figma 集成相关

```
POST /api/figma/connect
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| accessToken | string | true | Figma Personal Access Token |
| fileId | string | true | Figma 文件 ID |

```
GET /api/figma/tokens
```

响应参数:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| tokens | array | 设计令牌数组 |
| lastSync | string | 最后同步时间 |

```
POST /api/figma/sync
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| force | boolean | false | 是否强制同步 |
| components | array | false | 指定同步的组件 ID 列表 |

#### 设计令牌管理

```
GET /api/tokens
```

响应参数:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| tokens | array | 所有设计令牌 |
| categories | array | 令牌分类 |

```
PUT /api/tokens/:id
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| name | string | true | 令牌名称 |
| value | string | true | 令牌值 |
| category | string | true | 令牌分类 |
| description | string | false | 令牌描述 |

#### 组件管理

```
GET /api/components
```

响应参数:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| components | array | 所有组件信息 |
| total | number | 组件总数 |

```
POST /api/components/:id/generate
```

请求参数:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| format | string | true | 代码格式 (html/react/vue) |
| includeStyles | boolean | false | 是否包含样式 |

响应参数:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| code | string | 生成的代码 |
| dependencies | array | 依赖的设计令牌 |

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端/前端] --> B[控制器层]
    B --> C[服务层]
    C --> D[数据访问层]
    D --> E[(数据库)]
    
    C --> F[Figma API 服务]
    C --> G[缓存服务]
    C --> H[文件服务]
    
    F --> I[Figma REST API]
    G --> J[(Redis 缓存)]
    H --> K[本地文件系统]

    subgraph 服务器
        B
        C
        D
        F
        G
        H
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    USER ||--o{ PROJECT_MEMBER : belongs_to
    PROJECT ||--o{ PROJECT_MEMBER : has
    PROJECT ||--o{ FIGMA_FILE : contains
    PROJECT ||--o{ DESIGN_TOKEN : has
    PROJECT ||--o{ COMPONENT : has
    FIGMA_FILE ||--o{ DESIGN_TOKEN : generates
    FIGMA_FILE ||--o{ COMPONENT : generates
    DESIGN_TOKEN ||--o{ TOKEN_VERSION : has_versions
    COMPONENT ||--o{ COMPONENT_VERSION : has_versions
    USER ||--o{ SYNC_LOG : creates
    PROJECT ||--o{ SYNC_LOG : belongs_to

    USER {
        uuid id PK
        string email
        string password_hash
        string name
        string role
        timestamp created_at
        timestamp updated_at
    }
    
    PROJECT {
        uuid id PK
        string name
        string description
        json settings
        uuid owner_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    PROJECT_MEMBER {
        uuid id PK
        uuid project_id FK
        uuid user_id FK
        string role
        timestamp joined_at
    }
    
    FIGMA_FILE {
        uuid id PK
        uuid project_id FK
        string figma_file_id
        string name
        string access_token
        timestamp last_sync
        json sync_settings
        timestamp created_at
        timestamp updated_at
    }
    
    DESIGN_TOKEN {
        uuid id PK
        uuid project_id FK
        uuid figma_file_id FK
        string name
        string category
        string type
        string value
        json metadata
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    COMPONENT {
        uuid id PK
        uuid project_id FK
        uuid figma_file_id FK
        string figma_component_id
        string name
        string description
        json properties
        json generated_code
        timestamp created_at
        timestamp updated_at
    }
    
    TOKEN_VERSION {
        uuid id PK
        uuid token_id FK
        string value
        string change_reason
        uuid created_by FK
        timestamp created_at
    }
    
    COMPONENT_VERSION {
        uuid id PK
        uuid component_id FK
        json properties
        json generated_code
        string version_tag
        uuid created_by FK
        timestamp created_at
    }
    
    SYNC_LOG {
        uuid id PK
        uuid project_id FK
        uuid user_id FK
        string sync_type
        string status
        json details
        timestamp started_at
        timestamp completed_at
    }
```

### 6.2 数据定义语言

#### 用户表 (users)
```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'developer' CHECK (role IN ('designer', 'developer', 'admin')),
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- 设置权限
GRANT SELECT ON users TO anon;
GRANT ALL PRIVILEGES ON users TO authenticated;
```

#### 项目表 (projects)
```sql
-- 创建项目表
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    settings JSON DEFAULT '{}',
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_projects_owner_id ON projects(owner_id);
CREATE INDEX idx_projects_created_at ON projects(created_at DESC);

-- 设置权限
GRANT SELECT ON projects TO anon;
GRANT ALL PRIVILEGES ON projects TO authenticated;
```

#### 项目成员表 (project_members)
```sql
-- 创建项目成员表
CREATE TABLE project_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    permissions JSON DEFAULT '{}',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, user_id)
);

-- 创建索引
CREATE INDEX idx_project_members_project_id ON project_members(project_id);
CREATE INDEX idx_project_members_user_id ON project_members(user_id);

-- 设置权限
GRANT ALL PRIVILEGES ON project_members TO authenticated;
```

#### Figma 文件表 (figma_files)
```sql
-- 创建 Figma 文件表
CREATE TABLE figma_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    figma_file_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    access_token_encrypted TEXT NOT NULL,
    last_sync TIMESTAMP WITH TIME ZONE,
    sync_settings JSON DEFAULT '{}',
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'syncing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, figma_file_id)
);

-- 创建索引
CREATE INDEX idx_figma_files_project_id ON figma_files(project_id);
CREATE INDEX idx_figma_files_last_sync ON figma_files(last_sync DESC);

-- 设置权限
GRANT ALL PRIVILEGES ON figma_files TO authenticated;
```

#### 设计令牌表 (design_tokens)
```sql
-- 创建设计令牌表
CREATE TABLE design_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    figma_file_id UUID REFERENCES figma_files(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('color', 'typography', 'spacing', 'border', 'shadow', 'other')),
    value TEXT NOT NULL,
    css_variable VARCHAR(255),
    metadata JSON DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_design_tokens_project_id ON design_tokens(project_id);
CREATE INDEX idx_design_tokens_category ON design_tokens(category);
CREATE INDEX idx_design_tokens_type ON design_tokens(type);

-- 设置权限
GRANT ALL PRIVILEGES ON design_tokens TO authenticated;
```

#### 组件表 (components)
```sql
-- 创建组件表
CREATE TABLE components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    figma_file_id UUID REFERENCES figma_files(id) ON DELETE CASCADE,
    figma_component_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    properties JSON DEFAULT '{}',
    generated_code JSON DEFAULT '{}',
    preview_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_components_project_id ON components(project_id);
CREATE INDEX idx_components_figma_file_id ON components(figma_file_id);
CREATE INDEX idx_components_category ON components(category);

-- 设置权限
GRANT ALL PRIVILEGES ON components TO authenticated;
```

#### 同步日志表 (sync_logs)
```sql
-- 创建同步日志表
CREATE TABLE sync_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    figma_file_id UUID REFERENCES figma_files(id) ON DELETE CASCADE,
    sync_type VARCHAR(50) NOT NULL CHECK (sync_type IN ('manual', 'automatic', 'webhook')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('started', 'in_progress', 'completed', 'failed')),
    details JSON DEFAULT '{}',
    error_message TEXT,
    tokens_synced INTEGER DEFAULT 0,
    components_synced INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX idx_sync_logs_project_id ON sync_logs(project_id);
CREATE INDEX idx_sync_logs_started_at ON sync_logs(started_at DESC);
CREATE INDEX idx_sync_logs_status ON sync_logs(status);

-- 设置权限
GRANT ALL PRIVILEGES ON sync_logs TO authenticated;
```

#### 初始化数据
```sql
-- 插入默认管理员用户
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$10$example_hash', 'GMH Admin', 'admin'),
('<EMAIL>', '$2b$10$example_hash', 'GMH Designer', 'designer'),
('<EMAIL>', '$2b$10$example_hash', 'GMH Developer', 'developer');

-- 插入示例项目
INSERT INTO projects (name, description, owner_id) VALUES 
('GMH Design System', 'GMH 品牌设计系统集成项目', (SELECT id FROM users WHERE email = '<EMAIL>'));

-- 插入项目成员
INSERT INTO project_members (project_id, user_id, role) VALUES 
((SELECT id FROM projects WHERE name = 'GMH Design System'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'owner'),
((SELECT id FROM projects WHERE name = 'GMH Design System'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'admin'),
((SELECT id FROM projects WHERE name = 'GMH Design System'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'member');
```